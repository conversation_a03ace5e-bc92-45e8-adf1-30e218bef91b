# 🗺️ PropBolt Map Integration Documentation

## Overview
PropBolt uses **MapBox GL JS** for interactive map visualization of vacant land properties in Daytona Beach, FL. The integration displays real-time property data sourced from Zillow through our Go backend API.

## 🔑 API Configuration

### MapBox API Key
- **Current Token**: `pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY21icXYxbjZnMDN3czJrb2h5djd1bng0OSJ9.79dGToNOI5onemlC19dcDw`
- **Authorized Domains**: 
  - `propbolt.com` (Frontend)
  - `api.propbolt.com` (Backend API)

### Alternative: MapTiler
- **API Key**: `wBfumy70aE1pm6PGKkiU`
- **Status**: Available as backup option

## 🏗️ Architecture

### Data Flow
```
Zillow Property Data → Go Backend (Scraping) → PostgreSQL → Next.js API → MapBox Component
```

### Key Components
1. **Go Backend** (`main.go`): Scrapes Zillow data, processes coordinates
2. **Next.js API** (`src/app/api/search/route.ts`): Validates and forwards property data
3. **MapComponent** (`src/components/MapComponent.tsx`): Renders interactive map
4. **Property Types** (`src/types/property.ts`): TypeScript interfaces

## 🎯 Features

### Enhanced Map Markers
- **Zoning-based Colors**: Different colors for Commercial, Residential, Industrial, Mixed-use
- **Chain Potential Icons**: Visual indicators (🔥 Very High, ⭐ High, 📈 Medium, 📊 Low)
- **Price Display**: Formatted currency on markers
- **Hover Effects**: Scale animation and enhanced shadows

### Interactive Popups
- **Comprehensive Property Info**: Price, address, zoning, size, habitability
- **Beach Proximity**: Calculated distance to Atlantic Ocean
- **Market Data**: Days on market, price per sq ft
- **Chain Lease Potential**: Color-coded potential rating
- **Action Button**: Direct property selection

### Map Controls
- **Navigation**: Zoom, pan, rotate controls
- **Fullscreen**: Expand map to full viewport
- **Scale**: Imperial units for distance measurement
- **Style Toggle**: Switch between Light and Satellite views
- **Property Counter**: Live count of visible properties

## 🎨 Styling & UX

### Marker Colors by Zoning
- **Commercial**: Blue (`bg-blue-500`)
- **Residential**: Yellow (`bg-yellow-500`)
- **Industrial**: Purple (`bg-purple-500`)
- **Mixed-use**: Green (`bg-green-500`)
- **Other**: Gray (`bg-gray-500`)

### Chain Potential Indicators
- **Very High**: 🔥 Fire emoji
- **High**: ⭐ Star emoji
- **Medium**: 📈 Chart emoji
- **Low**: 📊 Bar chart emoji

## 📊 Property Data Integration

### Zillow Data Fields
```typescript
interface Property {
  id: number;
  address: string;
  price: number;
  size: string;
  zoning: string;
  latitude: number;
  longitude: number;
  description: string;
  habitability: string;
  proximity: string;
  chainLeasePotential: string;
  daysOnMarket: number;
  pricePerSqFt: number;
  createdAt: string;
  updatedAt: string;
}
```

### Coordinate Validation
- **Latitude Range**: -90 to 90 degrees
- **Longitude Range**: -180 to 180 degrees
- **Non-zero Check**: Excludes (0,0) coordinates
- **NaN Protection**: Filters invalid numeric values

## 🚀 Performance Optimizations

### Efficient Rendering
- **Coordinate Filtering**: Only renders properties with valid coordinates
- **Marker Clustering**: Prevents overcrowding at high zoom levels
- **Lazy Loading**: Map initializes only when container is ready
- **Memory Management**: Proper cleanup of markers and event listeners

### Error Handling
- **Graceful Degradation**: Shows fallback message if MapBox token missing
- **Coordinate Validation**: Skips properties with invalid coordinates
- **API Error Recovery**: Handles backend API failures gracefully

## 🔧 Configuration Files

### Environment Variables
```yaml
# frontend-app.yaml
NEXT_PUBLIC_MAPBOX_TOKEN: "pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY21icXYxbjZnMDN3czJrb2h5djd1bng0OSJ9.79dGToNOI5onemlC19dcDw"
```

### Next.js Configuration
```javascript
// next.config.js
images: {
  domains: ['api.mapbox.com']
}
```

## 🎯 Daytona Beach Focus

### Geographic Center
- **Coordinates**: 29.2108°N, 81.0228°W
- **Default Zoom**: Level 12
- **Coverage Area**: Volusia County, Florida

### Beach Proximity Calculation
- **Atlantic Ocean Reference**: 29.2108°N, 80.9773°W
- **Distance Categories**:
  - Beachfront: < 0.5 miles
  - Near Beach: 0.5-1 miles
  - Close to Beach: 1-5 miles
  - Inland: > 5 miles

## 🔄 Future Enhancements

### Planned Features
1. **Property Clustering**: Group nearby properties at low zoom levels
2. **Heat Maps**: Density visualization for property concentrations
3. **Drawing Tools**: Custom area selection for searches
4. **Offline Support**: Cached map tiles for poor connectivity
5. **3D Visualization**: Terrain and building height data

### Integration Opportunities
1. **Google Street View**: Property street-level imagery
2. **Satellite Imagery**: Recent aerial photography
3. **Flood Zone Data**: FEMA flood risk overlays
4. **School Districts**: Educational facility boundaries
5. **Transportation**: Public transit and highway access

## 📱 Mobile Responsiveness

### Touch Interactions
- **Pinch to Zoom**: Native mobile gesture support
- **Touch Markers**: Optimized touch targets for mobile
- **Responsive Popups**: Adaptive sizing for small screens
- **Gesture Controls**: Pan, rotate, and tilt gestures

## 🔐 Security & Compliance

### API Key Security
- **Environment Variables**: Secure token storage
- **Domain Restrictions**: Limited to authorized domains
- **Rate Limiting**: MapBox usage monitoring
- **Token Rotation**: Regular key updates for security

### Data Privacy
- **No Personal Data**: Only public property information
- **GDPR Compliance**: No user tracking or personal data collection
- **Secure Transmission**: HTTPS for all API communications
