'use client';

import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import {
  formatPrice,
  isValidCoordinate,
  filterPropertiesWithValidCoords,
  getZoningColor,
  getChainPotentialColor,
  calculateProximityToBeach
} from '@/lib/utils';
import { DAYTONA_BEACH_CENTER } from '@/lib/utils';
import type { Property } from '@/types/property';

// Set Mapbox access token
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || '';

// Helper functions for enhanced map markers
function getZoningColorForMarker(zoning: string): { bg: string; text: string } {
  const zoningLower = zoning.toLowerCase();

  if (zoningLower.includes('commercial')) {
    return { bg: 'bg-blue-500', text: 'text-white' };
  } else if (zoningLower.includes('residential')) {
    return { bg: 'bg-yellow-500', text: 'text-white' };
  } else if (zoningLower.includes('industrial')) {
    return { bg: 'bg-purple-500', text: 'text-white' };
  } else if (zoningLower.includes('mixed')) {
    return { bg: 'bg-green-500', text: 'text-white' };
  }

  return { bg: 'bg-gray-500', text: 'text-white' };
}

function getChainPotentialIcon(potential: string): string {
  const potentialLower = potential.toLowerCase();

  if (potentialLower.includes('very high')) {
    return '🔥';
  } else if (potentialLower.includes('high')) {
    return '⭐';
  } else if (potentialLower.includes('medium')) {
    return '📈';
  } else if (potentialLower.includes('low')) {
    return '📊';
  }

  return '🏢';
}

interface MapComponentProps {
  properties: Property[];
  selectedProperty: Property | null;
  onPropertyClick: (propertyId: number) => void;
}

export function MapComponent({ properties, selectedProperty, onPropertyClick }: MapComponentProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const markers = useRef<mapboxgl.Marker[]>([]);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [mapStyle, setMapStyle] = useState<'light' | 'satellite'>('light');

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/light-v11',
      center: [DAYTONA_BEACH_CENTER.lng, DAYTONA_BEACH_CENTER.lat],
      zoom: 12,
      attributionControl: false, // We'll add custom attribution
    });

    map.current.on('load', () => {
      setIsMapLoaded(true);
    });

    // Add navigation controls
    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');

    // Add fullscreen control
    map.current.addControl(new mapboxgl.FullscreenControl(), 'top-right');

    // Add scale control
    map.current.addControl(new mapboxgl.ScaleControl({
      maxWidth: 100,
      unit: 'imperial'
    }), 'bottom-left');

    // Add custom attribution
    map.current.addControl(new mapboxgl.AttributionControl({
      customAttribution: 'Property data from Zillow | PropBolt Vacant Land Search'
    }), 'bottom-right');

    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, []);

  // Update markers when properties change
  useEffect(() => {
    if (!map.current || !isMapLoaded) return;

    // Clear existing markers
    markers.current.forEach(marker => marker.remove());
    markers.current = [];

    // Filter properties with valid coordinates
    const validProperties = filterPropertiesWithValidCoords(properties);

    // Add new markers for valid properties
    validProperties.forEach(property => {
      // Get zoning-based styling
      const zoningColor = getZoningColorForMarker(property.zoning);
      const chainPotentialIcon = getChainPotentialIcon(property.chainLeasePotential);

      // Create custom marker element with enhanced styling
      const markerElement = document.createElement('div');
      markerElement.className = 'custom-marker';
      markerElement.innerHTML = `
        <div class="relative">
          <div class="${zoningColor.bg} ${zoningColor.text} px-3 py-2 rounded-lg shadow-lg text-sm font-bold cursor-pointer hover:shadow-xl transition-all duration-200 border-2 border-white">
            <div class="flex items-center gap-1">
              ${chainPotentialIcon}
              <span>${formatPrice(property.price)}</span>
            </div>
            <div class="text-xs opacity-90 mt-1">${property.zoning}</div>
          </div>
          <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
        </div>
      `;

      // Create enhanced popup with comprehensive property information
      const beachDistance = calculateProximityToBeach(property.latitude, property.longitude);
      const pricePerSqFt = property.pricePerSqFt > 0 ? `$${property.pricePerSqFt.toFixed(2)}/sq ft` : 'N/A';

      const popup = new mapboxgl.Popup({
        offset: 25,
        closeButton: true,
        closeOnClick: false,
        maxWidth: '350px',
      }).setHTML(`
        <div class="p-4 max-w-sm">
          <div class="flex justify-between items-start mb-3">
            <h4 class="font-bold text-zillow-blue text-lg">${formatPrice(property.price)}</h4>
            <span class="text-xs ${getZoningColor(property.zoning)} px-2 py-1 rounded-full">${property.zoning}</span>
          </div>

          <p class="font-medium text-gray-800 mb-2">${property.address}</p>
          <p class="text-sm text-gray-600 mb-3">${property.description}</p>

          <div class="grid grid-cols-2 gap-3 text-xs mb-3">
            <div class="space-y-1">
              <div><strong>Size:</strong> ${property.size}</div>
              <div><strong>Price/sq ft:</strong> ${pricePerSqFt}</div>
              <div><strong>Days on Market:</strong> ${property.daysOnMarket}</div>
            </div>
            <div class="space-y-1">
              <div><strong>Habitability:</strong> ${property.habitability}</div>
              <div><strong>Beach Distance:</strong> ${beachDistance}</div>
              <div><strong>Proximity:</strong> ${property.proximity}</div>
            </div>
          </div>

          <div class="border-t pt-2">
            <div class="flex items-center justify-between">
              <span class="text-xs font-medium">Chain Lease Potential:</span>
              <span class="text-xs ${getChainPotentialColor(property.chainLeasePotential)} px-2 py-1 rounded-full">
                ${property.chainLeasePotential}
              </span>
            </div>
          </div>

          <button
            onclick="window.dispatchEvent(new CustomEvent('property-select', { detail: ${property.id} }))"
            class="w-full mt-3 bg-zillow-blue text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-zillow-blue-dark transition-colors"
          >
            View Details
          </button>
        </div>
      `);

      // Create marker with error handling
      try {
        const marker = new mapboxgl.Marker(markerElement)
          .setLngLat([property.longitude, property.latitude])
          .setPopup(popup)
          .addTo(map.current!);

        // Add click handler
        markerElement.addEventListener('click', () => {
          onPropertyClick(property.id);
        });

        markers.current.push(marker);
      } catch (error) {
        console.warn(`Failed to create marker for property ${property.id}:`, error);
      }
    });

    // Fit map to show all markers with proper error handling
    if (validProperties.length > 0) {
      try {
        const bounds = new mapboxgl.LngLatBounds();
        let hasValidBounds = false;

        validProperties.forEach(property => {
          try {
            bounds.extend([property.longitude, property.latitude]);
            hasValidBounds = true;
          } catch (error) {
            console.warn(`Failed to extend bounds for property ${property.id}:`, error);
          }
        });

        // Only fit bounds if we have valid bounds
        if (hasValidBounds && bounds.getNorthEast() && bounds.getSouthWest()) {
          map.current.fitBounds(bounds, {
            padding: 50,
            maxZoom: 15,
            duration: 1000,
          });
        } else {
          // Fallback to Daytona Beach center if no valid bounds
          console.warn('No valid bounds found, centering on Daytona Beach');
          map.current.flyTo({
            center: [DAYTONA_BEACH_CENTER.lng, DAYTONA_BEACH_CENTER.lat],
            zoom: 12,
            duration: 1000,
          });
        }
      } catch (error) {
        console.error('Error fitting bounds:', error);
        // Fallback to Daytona Beach center
        map.current.flyTo({
          center: [DAYTONA_BEACH_CENTER.lng, DAYTONA_BEACH_CENTER.lat],
          zoom: 12,
          duration: 1000,
        });
      }
    } else if (properties.length > 0) {
      // If we have properties but none with valid coordinates, show a warning
      console.warn(`Found ${properties.length} properties but none have valid coordinates`);
      // Center on Daytona Beach
      map.current.flyTo({
        center: [DAYTONA_BEACH_CENTER.lng, DAYTONA_BEACH_CENTER.lat],
        zoom: 12,
        duration: 1000,
      });
    }
  }, [properties, isMapLoaded, onPropertyClick]);

  // Handle selected property
  useEffect(() => {
    if (!map.current || !selectedProperty) return;

    // Validate selected property coordinates
    const hasValidCoords = isValidCoordinate(selectedProperty.latitude, selectedProperty.longitude);

    if (!hasValidCoords) {
      console.warn(`Selected property ${selectedProperty.id} has invalid coordinates:`, {
        lat: selectedProperty.latitude,
        lng: selectedProperty.longitude
      });
      return;
    }

    try {
      // Center map on selected property
      map.current.flyTo({
        center: [selectedProperty.longitude, selectedProperty.latitude],
        zoom: 16,
        duration: 1000,
      });

      // Open popup for selected property
      const selectedMarker = markers.current.find(marker => {
        try {
          const lngLat = marker.getLngLat();
          return Math.abs(lngLat.lat - selectedProperty.latitude) < 0.0001 &&
                 Math.abs(lngLat.lng - selectedProperty.longitude) < 0.0001;
        } catch (error) {
          console.warn('Error getting marker coordinates:', error);
          return false;
        }
      });

      if (selectedMarker) {
        try {
          selectedMarker.togglePopup();
        } catch (error) {
          console.warn('Error toggling popup:', error);
        }
      }
    } catch (error) {
      console.error('Error handling selected property:', error);
    }
  }, [selectedProperty]);

  // Add event listener for property selection from popup
  useEffect(() => {
    const handlePropertySelect = (event: CustomEvent) => {
      onPropertyClick(event.detail);
    };

    window.addEventListener('property-select', handlePropertySelect as EventListener);
    return () => {
      window.removeEventListener('property-select', handlePropertySelect as EventListener);
    };
  }, [onPropertyClick]);

  if (!process.env.NEXT_PUBLIC_MAPBOX_TOKEN) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <p className="text-gray-600 mb-2">Map unavailable</p>
          <p className="text-sm text-gray-500">Mapbox token not configured</p>
        </div>
      </div>
    );
  }

  // Toggle map style function
  const toggleMapStyle = () => {
    if (!map.current) return;

    const newStyle = mapStyle === 'light' ? 'satellite' : 'light';
    const styleUrl = newStyle === 'light'
      ? 'mapbox://styles/mapbox/light-v11'
      : 'mapbox://styles/mapbox/satellite-v9';

    map.current.setStyle(styleUrl);
    setMapStyle(newStyle);
  };

  return (
    <div className="w-full h-full relative">
      <div ref={mapContainer} className="w-full h-full" />

      {/* Map Style Toggle */}
      <div className="absolute top-4 left-4 z-10">
        <button
          onClick={toggleMapStyle}
          className="bg-white shadow-lg rounded-lg px-3 py-2 text-sm font-medium hover:bg-gray-50 transition-colors border"
        >
          {mapStyle === 'light' ? '🛰️ Satellite' : '🗺️ Map'}
        </button>
      </div>

      {/* Property Count Badge */}
      {properties.length > 0 && (
        <div className="absolute top-4 right-20 z-10">
          <div className="bg-zillow-blue text-white px-3 py-2 rounded-lg shadow-lg text-sm font-medium">
            {filterPropertiesWithValidCoords(properties).length} Properties
          </div>
        </div>
      )}

      {/* Loading Spinner */}
      {!isMapLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="loading-spinner" />
        </div>
      )}
    </div>
  );
}
